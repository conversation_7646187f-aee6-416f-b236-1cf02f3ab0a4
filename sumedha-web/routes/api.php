<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Api\V1\AuthController;
use App\Http\Controllers\Api\V1\StudentController;
use App\Http\Controllers\Api\V1\DocumentController;
use App\Http\Controllers\Api\V1\CourseController;

// API Version 1 Routes
Route::prefix('v1')->group(function () {

    // Public Authentication Routes
    Route::prefix('auth')->group(function () {
        Route::post('admin/login', [AuthController::class, 'adminLogin']);
        Route::post('student/login', [AuthController::class, 'studentLogin']);
    });

    // Protected Admin Routes
    Route::middleware(['auth:sanctum', 'institute.access'])->prefix('admin')->group(function () {
        Route::post('logout', [AuthController::class, 'adminLogout']);
        Route::get('user', [AuthController::class, 'adminUser']);

        // Student management routes
        Route::apiResource('students', StudentController::class);

        // Document management routes
        Route::get('document-types', [DocumentController::class, 'getDocumentTypes']);
        Route::get('students/{student}/documents', [DocumentController::class, 'getStudentDocuments']);
        Route::post('documents/upload', [DocumentController::class, 'uploadDocument']);

        // Course management routes
        Route::apiResource('courses', CourseController::class);
        Route::post('courses/assign-student', [CourseController::class, 'assignToStudent']);

        // Institute management routes will be added here
        // Chat routes will be added here
        // Other admin routes will be added here
    });

    // Protected Student Routes
    Route::middleware(['auth:sanctum'])->prefix('student')->group(function () {
        Route::post('logout', [AuthController::class, 'studentLogout']);
        Route::get('user', [AuthController::class, 'studentUser']);

        // Student-specific routes will be added here
        // Document upload routes will be added here
        // Chat routes will be added here
        // Learning content routes will be added here
    });

    // Public routes (for student registration via referral)
    Route::prefix('public')->group(function () {
        // Student registration routes will be added here
    });
});

// Legacy route for backward compatibility
Route::middleware(['auth:sanctum'])->get('/user', function (Request $request) {
    return $request->user();
});
